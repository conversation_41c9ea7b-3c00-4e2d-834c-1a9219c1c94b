<!DOCTYPE html>
<html>
<head>
    <title>Wizard - FM-DX Webserver</title>
    <link href="css/entry.css" type="text/css" rel="stylesheet">
    <link href="css/flags.min.css" type="text/css" rel="stylesheet">
    <link href="css/libs/fontawesome.css" type="text/css" rel="stylesheet">
    <script src="js/libs/jquery.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.css" type="text/css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.js"></script>
    <link rel="icon" type="image/png" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body>
    <div id="toast-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>
    <div class="wrapper-outer wrapper-full">
    <div id="wrapper">
        <div class="panel-100 no-bg">
            <img class="top-10" src="../images/openradio_logo_neutral.png" height="64px">
            <h2 class="text-monospace text-light text-gray">[SETUP WIZARD]</h2>
        </div>
        <div class="panel-100 no-bg flex-container flex-center flex-phone">
            <div class="btn-rounded-cube activated">1</div>
            <div class="btn-rounded-cube">2</div>
            <div class="btn-rounded-cube">3</div>
            <div class="btn-rounded-cube">4</div>
            <div class="btn-rounded-cube">5</div>
        </div>

        <div class="panel-100">
            <!-- BASIC SETTINGS -->
            <div class="panel-100 step no-bg" id="step1">
                <h2 class="settings-heading">Basic settings</h2>
                <p class="m-0">Welcome to the setup wizard! Let's set up some basic things.</p>

                <h3 class="settings-heading">Webserver connection</h3>
                <p class="m-0">Leave the IP at 0.0.0.0 unless you explicitly know you have to change it.<br>DO NOT enter your public IP here.</p>
                <div class="flex-center top-25">
                    <%- include('_components', {component: 'text', cssClass: 'w-150 br-15', placeholder: '0.0.0.0', label: 'Webserver IP', id: 'webserver-webserverIp'}) %>
                    <%- include('_components', {component: 'text', cssClass: 'w-100 br-15', placeholder: '8080', label: 'Webserver port', id: 'webserver-webserverPort'}) %><br>
                </div>
            </div>
            <!-- BASIC SETTINGS END -->
            <!-- TUNER SETTINGS -->
            <div id="step2" class="step" style="display: none">
                <h2 class="settings-heading">Tuner settings</h2>

                <h3 class="settings-heading">Tuner type</h3>
                <p class="m-0">Settings a proper device type ensures that the correct interface and settings will load.</p>
                <div class="panel-100 no-bg flex-center">
                    <%- include('_components', { component: 'dropdown', id: 'device-selector', inputId: 'device', label: 'Device', cssClass: '', placeholder: 'TEF668x / TEA685x', 
                    options: [
                        { value: 'tef', label: 'TEF668x / TEA685x' },
                        { value: 'xdr', label: 'XDR (F1HD / S10HDiP)' },
                        { value: 'sdr', label: 'SDR (RTL-SDR / AirSpy)' },
                        { value: 'other', label: 'Other' }
                    ]
                }) %><br>
                </div>
                <div class="clearfix"></div>
                <h3 class="settings-heading">Tuner connection</h3>
                <div style="width: 300px;" class="auto top-10">
                    <label class="toggleSwitch nolabel" onclick="">
                        <input id="xdrd-wirelessConnection" type="checkbox" aria-label="Tuner connection type"/>
                        <a></a>
                        <span>
                            <span class="left-span">Direct</span>
                            <span class="right-span">TCP/IP</span>
                        </span>											
                    </label>
                </div>
                <div id="tuner-usb" class="top-25">
                    <p>It's time to choose your USB device.</p>
                    
                    <div class="panel-100 no-bg flex-center">
                        <%- include('_components', {
                            component: 'dropdown',
                            id: 'deviceList',
                            inputId: 'xdrd-comPort',
                            label: 'USB Device',
                            cssClass: '',
                            placeholder: 'Choose your USB device',
                            options: serialPorts.map(serialPort => ({
                                value: serialPort.path,
                                label: `${serialPort.path} - ${serialPort.friendlyName}`
                            }))
                        }) %> 
                    </div>
                </div>
                <div class="clearfix"></div>
                <div id="tuner-wireless" class="top-25">
                    <p class="m-0">If you are connecting your tuner <strong>wirelessly</strong>, enter the tuner IP. <br> If you use <strong>xdrd</strong>, use 127.0.0.1 as your IP.</p>
                    <div class="flex-center top-25">
                        <%- include('_components', {component: 'text', cssClass: 'w-150 br-15', label: 'xdrd IP address', id: 'xdrd-xdrdIp'}) %>
                        <%- include('_components', {component: 'text', cssClass: 'w-100 br-15', label: 'xdrd port', id: 'xdrd-xdrdPort'}) %>
                        <%- include('_components', {component: 'text', cssClass: 'w-150 br-15', label: 'xdrd password', id: 'xdrd-xdrdPassword', password: true}) %>
                    </div>
                </div>
            </div>
            <!-- TUNER SETTINGS END -->
            <!-- AUDIO SETTINGS -->
            <div id="step3" class="step" style="display: none;">
                <div class="panel-100 no-bg" style="min-height: 120px;margin-bottom: 0;">
                    <h2 class="settings-heading">Audio settings</h2>
                    <p class="m-0">In this section, we will set up the audio.<br>
                        Choose the audio port your tuner is connected to and desired audio settings here.</p>
                    <p class="text-gray">Recommended defaults have already been set for the audio quality, you can keep them as-is.</p>
                    
                    <div class="panel-100 no-bg p-bottom-20 flex-container flex-center">
                        <%- include('_components', {
                            component: 'dropdown',
                            id: 'audioList',
                            inputId: 'audio-audioDevice',
                            label: 'Audio device',
                            cssClass: '',
                            placeholder: 'Choose your audio device',
                            options: [
                                ...videoDevices.map(device => ({
                                    value: device.name,
                                    label: `${device.name}`
                                })),
                                ...audioDevices.map(device => ({
                                    value: device.name,
                                    label: `${device.name}`
                                }))
                            ]
                        }) %>  
                        
                        <%- include('_components', { component: 'dropdown', id: 'audio-channels-dropdown', inputId: 'audio-audioChannels', label: 'Audio channels', cssClass: '', placeholder: 'Stereo', 
                        options: [
                            { value: '2', label: 'Stereo' },
                            { value: '1', label: 'Mono' }
                        ]
                        }) %>
                        
                        <%- include('_components', { component: 'dropdown', id: 'audio-quality-dropdown', inputId: 'audio-audioBitrate', label: 'Audio quality', cssClass: '', placeholder: '128kbps (standard)', 
                        options: [
                            { value: '64k', label: '64kbps (lowest quality)' },
                            { value: '96k', label: '96kbps (low quality)' },
                            { value: '128k', label: '128kbps (standard)' },
                            { value: '192k', label: '192kbps (high quality)' },
                            { value: '256k', label: '256kbps (very high quality)' },
                            { value: '320k', label: '320kbps (ultra quality)' }
                        ]
                        }) %>
                    </div>

                    <div class="panel-100 no-bg text-center">
                        <div class="flex-container">
                            <div class="panel-50 no-bg">
                                <p>If you use an USB audio card on Linux, enabling this option might fix your audio issues.</p>
                                <%- include('_components', {component: 'checkbox', cssClass: 'panel-100 flex-container flex-center', label: 'ALSA Software mode', id: 'audio-softwareMode'}) %>
                            </div>
                            <div class="panel-50 no-bg">
                                <p>Legacy option for Linux / macOS that could resolve audio issues, but will consume additional CPU and RAM usage.</p>
                                <%- include('_components', {component: 'checkbox', cssClass: 'panel-100 flex-container flex-center', label: 'Additional FFmpeg', id: 'audio-ffmpeg'}) %>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- AUDIO SETTINGS END -->
            <!-- IDENTIFICATION START -->
            <div id="step4" class="step" style="display: none;">
                <div class="panel-100 no-bg" style="padding-bottom: 20px;">
                    <h2 class="settings-heading">Webserver info</h2>
                    <p class="m-0">In this part, we will set up your webserver info, such as the server name, description and location.</p>
                    <label for="identification-tunerName" style="width: 100%;max-width: 768px; margin:auto;">Webserver name:</label>
                    <input style="width: 100%; max-width: 768px;" class="input-text br-15" type="text" name="identification-tunerName" id="identification-tunerName" placeholder="Fill your server name here." maxlength="32">
                    <br>
                    <label for="identification-tunerDesc" style="width: 100%;max-width: 768px; margin: auto;">Webserver description:</label>
                    <textarea id="identification-tunerDesc" name="webserver-desc" class="br-15" placeholder="Fill the server description here. You can put useful info here such as your antenna setup. You can use simple markdown." maxlength="255"></textarea>
                    
                    <h3 class="settings-heading">Location</h3>
                    <p>Location info is useful for automatic identification of stations using RDS.</p>
                    <div class="panel-100 no-bg flex-container flex-center">
                        <%- include('_components', {component: 'text', cssClass: 'w-250 br-15', placeholder: '', label: 'Latitude', id: 'identification-lat'}) %>
                        <%- include('_components', {component: 'text', cssClass: 'w-250 br-15', placeholder: '', label: 'Longitude', id: 'identification-lon'}) %>
                    </div>
                    <div id="map"></div>

                    <h3 class="settings-heading">Map broadcast</h3>
                    <p class="m-0">If your location info is filled, you can add your tuner to a public list.</p>
                    <p class="m-0">The list is available at <strong><a href="https://servers.fmdx.org" target="_blank" class="color-4">servers.fmdx.org</a></strong>.</p>
                    <p class="text-small text-gray">By activating the <strong>Broadcast to map</strong> option, you agree to the <a href="https://fmdx.org/projects/webserver.php#rules" target="_blank">Terms of Service</a>.</p>

                    <div class="panel-100 no-bg flex-container flex-center">
                        <%- include('_components', {component: 'checkbox', cssClass: '', label: 'Broadcast to map', id: 'identification-broadcastTuner'}) %><br>
                        <%- include('_components', {component: 'checkbox', cssClass: '', label: 'Allow tuning without password', id: 'publicTuner'}) %>
                    </div>

                    <p class="text-gray">If you use a proxy / tunnel service, enter the access link here. If you don't know what a proxy is, leave it empty.</p>

                    <div class="panel-100 no-bg flex-container flex-center">
                        <%- include('_components', {component: 'text', cssClass: 'br-15', label: 'Broadcast address (if using a proxy)', id: 'identification-proxyIp'}) %>
                    </div>
                </div>
            </div>
            <!-- IDENTIFICATION END -->
            <!-- ADMIN SETTINGS START -->
            <div id="step5" class="step" style="display: none;">
                <h2 class="settings-heading">Admin panel settings</h2>
                <p>We are at the last and final step of the wizard.</p>

                <p>Here we can set the password. Tune password is optional.<br>Setting an admin password allows you to change settings later and setting one up is mandatory.</p>
                <div class="flex-container flex-center">
                    <%- include('_components', {component: 'text', cssClass: 'w-150 br-15', placeholder: '', label: 'Tune password', id: 'password-tunePass', password: true}) %>
                    <%- include('_components', {component: 'text', cssClass: 'w-150 br-15', placeholder: '', label: 'Admin password', id: 'password-adminPass', password: true}) %><br>
                </div>
                <p>You can now click the <strong>save button</strong> to save your settings. After that, you will need to restart the webserver.</p>
            </div>

            <div class="panel-100 no-bg">
                <button class="btn-prev"><i class="fa-solid fa-arrow-left"></i></button>
                <button class="btn-next">Next</button>
            </div>
        </div>
        <div class="panel-100 no-bg">
            <p>Feel free to contact us on <a href="https://discord.gg/ZAVNdS74mC" target="_blank"><strong><i class="fa-brands fa-discord"></i> Discord</strong></a> for community support.</p>
        </div>
    </div>
    </div>
    <script src="js/init.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/dropdown.js"></script>
    <script src="js/toast.js"></script>
    <script src="js/setup.js"></script>
    <script src="js/wizard.js"></script>
    <script src="js/confighandler.js"></script>
</body>
</html>
