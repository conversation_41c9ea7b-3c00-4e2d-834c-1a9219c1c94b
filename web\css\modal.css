body.modal-open {
    overflow: hidden;
}

.modal-panel, .modal-panel-chat {
    max-height: 100vh;
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6); /* Semi-transparent background */
    opacity: 0;
    transition: opacity 0.3s ease; /* Fade-in/out transition */
    z-index: 20; /* Ensure the modal is above other content */
    color: var(--color-4);
    backdrop-filter: blur(10px);
}

/* Style for the modal content */
.modal-content {
    box-sizing: border-box; 
    position: absolute;
    top: 50vh;
    left: 50vw;
    transform: translate(-50%, -50%);
    background-color: var(--color-main);
    padding: 30px;
    border-radius: 15px;
    opacity: 1;
    transition: opacity 0.3s ease; /* Fade-in/out transition */
    z-index: 21; /* Ensure the modal content is above the modal background */
    min-width: 500px;
}

.modal-title {
    font-size: 20px;
    position: absolute;
    font-weight: 300;
    top: 14px;
    left: 30px;
}

/* Style for the close button */
.close {
    position: absolute;
    top: 17px;
    right: 30px;
    cursor: pointer;
    transition: 0.3s ease color;
}

.close:hover {
    color: var(--color-text);
}

.modal-content .button-close {
    position: absolute;
    bottom: 25px;
    right: 35px;
    width: 100px;
    height: 48px;
    border-radius: 15px;
    background: var(--color-4);
    font-weight: bold;
    border: 0;
    transition: 0.35s ease background;
    cursor: pointer;
}

.modal-content .button-close:hover {
    background: var(--color-5);
}

.modal-panel {
    width: 450px;
    height: 100%;
    position: absolute;
    right: 0;
    text-align: center;
    display: none;
    background-color: var(--color-main);
}

.modal-panel h1 {
    font-size: 42px;
}

.modal-panel-sidebar {
    width: 64px;
    background-color: var(--color-1);
    cursor: pointer;
    padding: 5px;
}

.modal-panel-content {
    flex: 1;
    position: relative;
    overflow-y: auto;
}

.modal-panel-footer {
    width: 450px;
    height: 100px;
    background-color: var(--color-main);
    vertical-align: bottom;
}

.modal-panel .form-group {
    float: none !important;
}

.modal-panel .dropdown {
    margin: auto;
}

.modal-panel label {
    width: 200px;
}

.modal-panel-chat {
    width: 100%;
    max-width: 960px;
    height: 450px;
    position: absolute;
    bottom: 0;
    margin: auto;
    left: 0;
    right: 0;
    text-align: center;
    display: none;
    background-color: var(--color-main);
    border-radius: 15px 15px 0px 0px;
}

.modal-panel-chat .modal-panel-sidebar {
    width: 100%;
    border-radius: 15px 15px 0px 0px;
}

/* Popup Windows */
.popup-window {
    width: 500px;
    background: var(--color-1-transparent);
    backdrop-filter: blur(5px);
    position: absolute !important;
    border-radius: 15px;
    display: none;
    overflow: hidden;
    border: 3px solid var(--color-1);
    z-index: 100;
  }

  .popup-header {
    padding: 8px;
    cursor: move;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .popup-close {
    background: none;
    border: none;
    color: var(--color-3);
    font-size: 16px;
    cursor: pointer;
    max-width: 24px;
    margin-left: auto;
  }

@media only screen and (max-width: 768px) {
    .modal-content {
        min-width: 90% !important;
        margin: auto;
        position: static;
        transform: initial;
        overflow-y: auto;
        max-height: 100vh;
    }
    .modal-title {
        position: static;
    }
    .closeModalButton {
        position: static;
    }
    .modal-panel {
        width: 100%;
    }

    .popup-window {
        width: 90%;
        height: 60%;
    }
}

@media only screen and (max-height: 768px) { 
    .modal-panel .version-info {
        position: static;
        bottom: auto;
    }
    .modal-panel-content {
        overflow-y: auto;
    }
}