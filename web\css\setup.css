
.setup-wrapper .form-group, .setup-wrapper textarea {
    display: inline-block;
    float: none;
}

.setup-wrapper .form-group {
    margin-right: 5px;
    margin-left: 5px;
}

.setup-wrapper h2 {
    font-size: 42px;
    font-weight: 300;
    padding: 20px 15px;
    text-align: left;
}

#wrapper textarea {
    width: 100%;
    max-width: 768px;
    background-color: var(--color-2);
    height: 100px;
    font-size: 14px;
    padding-top: 10px;
}

.sidenav {
    background-color: var(--color-main);
  }

  .sidenav li a:focus {
    outline: none;
  }

  .sidenav-content {
    flex: 1;
    position: relative;
    overflow-y: auto;
  }
  
  .sidenav .closebtn {
    position: absolute;
    top: 0;
    right: 25px;
    font-size: 36px;
    margin-left: 50px;
  }

  .sidenav h1 {
    font-size: 42px;
    text-transform: initial;
    font-weight: 300;
    text-align: center;
  }
ul.nav {
    list-style-type: none;
    padding: 15px 0;
    border-radius: 15px;
}

ul.nav li {
    padding: 12px 20px;
    cursor: pointer;
    transition: color 0.3s ease, background-color 0.3s ease;
    user-select: none;
}

ul.nav li a {
    color: var(--color-5) !important;
}


ul.nav li:hover {
    background-color: var(--color-3);
}

ul.nav li:hover a {
    color: var(--color-main) !important;
}

ul.nav li.active a {
    color: var(--color-main) !important;
    font-weight: bold;
}


li.active {
    background-color: var(--color-4);
}

.tab-content {
    display: none;
}

#navigation {
    position: fixed;
    top: 0;
    left: 0;
    width: 420px; /* Width of the sidenav */
    height: 100%;
    z-index: 1000; /* Ensure it's above other content */
    transition: margin-left 0.3s ease; /* Smooth transition */
  }
  
  .admin-wrapper {
    transition: margin-left 0.3s ease, width 0.3s ease;
  }

  .admin-wrapper > .panel-full > .panel-full {
    min-height: 100vh;
  }

#map {
    height:400px;
    width:100%;
    overflow: hidden;
    max-width:800px;
    margin: auto;
    margin-bottom: 20px;
}

.setup-wrapper h3 {
    font-weight: 300;
    margin: 8px;
    font-size: 36px;
    color: var(--color-5)
}

.setup-wrapper h4 {
    color: var(--color-4);
}


#console-output {
    background-color: #111;
    height: 300px;
    overflow-y:auto;
}
.w-250 {
    width: 250px !important
}

.w-200 {
    width: 200px !important
}

.w-150 {
    width: 150px !important
}

.w-100 {
    width: 100px !important;
}

@media only screen and (max-width: 768px) {
    .setup-wrapper .panel-33, .setup-wrapper .panel-50 {
        background: var(--color-1-transparent);
    }
    #navigation {
        width: 100vw; /* You can make the sidenav full width on mobile if you want */
      }
      
      .admin-wrapper {
        margin-left: 0;
        width: 100%;
      }
}
