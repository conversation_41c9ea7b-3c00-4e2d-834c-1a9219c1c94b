h1 {
    color: var(--color-4);
    font-size: 32px;
    font-weight: 700;
    text-transform: uppercase;
    margin-top: 0;
    margin-bottom: 0;
}

.modal-panel-content h1 {
    text-transform: initial;
    font-weight: 300;
    font-size: 42px;
}

h1#tuner-name {
    font-size: 26px;
    font-weight: 300;
    text-transform: initial;
    user-select: none;
    cursor: pointer;
    transition: 0.3s ease color;
}

#tuner-name i {
    transition: transform 0.3s ease;
}

#tuner-name i.rotated {
    transform: rotate(180deg);
}


h1#tuner-name:hover {
    color: var(--color-main-bright);
}

h2 {
    color: var(--color-4);
    margin-bottom: 0;
    margin-top: 8px;
}

h3 {
    font-size: 22px;
    color: var(--color-4);
}

h4 {
    margin: 0;
    font-weight: 400;
    font-size: 20px;
}

.tooltip {
    display: inline-block;
    cursor: pointer;
}

.tooltiptext {
    position: absolute;
    background-color: var(--color-2);
    border: 2px solid var(--color-3);
    color: var(--color-text);
    text-align: center;
    font-size: 14px;
    border-radius: 15px;
    padding: 5px 25px;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

p.tuner-desc {
    margin: 0;
}

label {
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    display: block;
    color: var(--color-4);
}

.canvas-container {
    width: calc(100% - 20px);
    height: 140px;
    overflow: hidden;
    border-radius: 15px;
    margin-left: 10px;
    margin-right: 10px;
    position: relative;
}

#data-ps, #data-rt0, #data-rt1 {
    font-family: "Roboto Mono", monospace;
}

#data-rt0, #data-rt1 {
    font-size: 14px;
}

#data-ps {
    font-weight: 500;
    margin-top: -5px;
}

#data-station-container {
    display: none;
}

#data-station-container h2 {
    display: block !important;
    padding: 0;
}

#data-station-name {
    font-size: 20px;
}

#data-station-others span {
    color: var(--color-main);
    background: var(--color-4);
    margin-left: 4px;
    padding: 0 5px;
    border-radius: 3px;
}

.highest-signal-container {
    margin-bottom: -20px !important;
}

.form-group {
    float: left;
    margin-bottom: 10px;
    margin-left: 5px;
    margin-right: 5px;
}

table .form-group {
    margin: 0;
}

.hidden-panel {
    display: none; 
    position: absolute;
    left: 50%;
    transform:translateX(-50%);
    width: 100%;
    max-width: 1160px;
    background: var(--color-1-transparent);
    backdrop-filter: blur(5px);
    text-align: left;
    padding: 20px;
    z-index: 10;
    border-radius: 0 0 15px 15px;
}

.users-online-container {
    top: 80px;
}

#af-list {
    overflow-y: auto;
    max-height: 345px;
}

#af-list ul {
    display: list-item;
    padding: 0;
    list-style-type: none;
    margin: 0;
    font-size: 14px;
}

#af-list a {
    cursor: pointer;
}

#flags-container-phone {
    display: none;
}

#login-form {
    padding: 5px;
}

.form-group input:checked + label {
    background-color: var(--color-4);
    color: var(--color-main);
}

.tuner-info {
    margin-top: 0px !important;
    margin-bottom: 0px !important;
}

h2.settings-heading {
    font-size: 42px;
    padding: 10px 0;
    font-weight: 300;
}

h3.settings-heading {
    font-size: 24px;
    text-transform: uppercase;
    font-weight: 300;
    margin-bottom: 5px;
}

#tuner-wireless {
    display: none;
}

#flags-container-phone,
#flags-container-desktop {
    position: relative; /* Confine overlay within container which is necessary for iPhones */
}

#flags-container-phone .overlay,
#flags-container-desktop .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: auto;
    opacity: 0;
}

.admin-quick-dashboard {
    width: 255px;
    height: 72px;
    background-color: var(--color-2);
    margin: 0 auto;
    margin-bottom: 15px;
    padding: 5px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.admin-quick-dashboard .icon {
    width: 52px;
    height: 52px;
    margin: auto;
    color: var(--color-1);
    background-color: var(--color-3);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 18px;
    border-radius: 10px;
    transition: 0.3s ease background-color;
    cursor: pointer;
}

.admin-quick-dashboard .icon.active {
    background-color: var(--color-4);
}

.admin-quick-dashboard .icon:hover {
    background-color: var(--color-5);
}

pre {
    color: white;
}

.circle-container {
    position: relative;
    width: 25px;
    height: 34px;
}

.circle {
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid var(--color-text);
    border-radius: 50%;
}

.circle.circle1 {
    left: 0px;
    top: 10px;
}

.circle.circle2 {
    left: 8px;
    top: 10px;
}

.flex-container.contains-dropdown {
    z-index: 999;
    position: relative;
}

.text-200-px {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 200px;
}

@media (min-width: 768px) { 
    .users-online-container {
        margin-left: 10px;
    }
}

@media (max-width: 768px) {
    canvas, #flags-container {
        display: none;
    }
    .tuner-desc {
        margin-bottom: 20px !important;
        text-align: center;
    }
    #ps-container {
        background-color: var(--color-1-transparent);
        height: 100px !important;
        margin: auto !important;
        margin-top: 30px !important;
        width: 100%;
    }
    h2 {
        display: none;
    }
    .form-group {
        float: none;
    }
    .highest-signal-container {
        margin-top: -20px !important;
        margin-bottom: -15px !important;
    }
    #pi-code-container {
        margin-bottom: 0 !important;
    }
    #data-pi {
        font-size: 24px;
        margin-top: 5px;
        color: var(--color-text-2);
    }
    h2.show-phone {
        display: inline;
        padding: 0 !important;
    }
    #data-ps {
        font-size: 42px;
    }
    #data-frequency {
        font-size: 58px;
    }
    #data-rt0, #data-rt1 {
        font-size: 10px;
        text-align: left;
        width: 100%;
        white-space: nowrap;
        overflow-x:auto;
        padding: 5px 0;
    }
    #rt-container {
        height: max-content !important;
    }
    ul {
        font-size: 16px;
    }
    #af-list ul li {
        display: inline-table;
        margin-right: 5px;
        width: 40px;
    }
    #flags-container-desktop {
        display: none;
    }
    #flags-container-phone {
        display: block;
    }
    #tune-buttons {
        height: 64px;
        order: 1;
    }
    .filter-controls {
        order: 2;
    }
    .button-eq {
        order: 1;
    }
    .button-ims {
        order: 3;
    }
    .tuner-info {
        margin-bottom: -60px !important;
    }
    #af-list ul {
        height: auto !important;
    }
    #rt-container {
        order: 2;
    }
    .modal-panel-footer {
        width: auto !important;
    }
    #mobileTray {
        width: 100%;
        height: 68px;
        position: fixed;
        bottom: 0;
        background-color: var(--color-1-transparent);
        backdrop-filter: blur(5px);
        display: flex;
        align-items: center;
    }
}

@media only screen and (min-width: 769px) and (max-height: 720px) {
    #rt-container {
        height: 90px !important;
    }
    #ps-container {
        margin-left: 15px;
    }
    .canvas-container {
        height: 120px;
    }
    .tuner-info #tuner-name {
        float: left;
        font-size: 24px;
        text-align: left;
    }
    
    .tuner-info #tuner-limit {
        float: left;
        text-align: left;
    }

    h2 {
        margin-bottom: 10px;
        font-size: 18px;
    }
    
    h2.signal-heading {
        margin-bottom: 0;
    }
    
    .highest-signal-container {
        margin-bottom: -10px !important;
    }
    
    h2.mb-0 {
        margin-bottom: 0;
        margin-top: 2px !important;
    }
    
    #af-list {
        overflow-y: auto;
        max-height: 330px;
    }
}

@media only screen and (min-width: 769px) and (max-height: 610px) {
    .canvas-container {
        display: none;
    }
}