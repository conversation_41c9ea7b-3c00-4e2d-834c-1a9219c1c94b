button, input[type="submit"] {
    width: 100%;
    height: 100%;
    border: 0;
    border-radius: 15px;
    transition: 0.3s ease background-color;
    background-color: var(--color-4);
    cursor: pointer;
    font-family: inherit;
    color: var(--color-main);
}

button:hover {
    background-color: var(--color-main-bright);
}

.btn-disabled {
    opacity: 0.6;
}

.cursor-disabled {
    cursor: not-allowed;
}

.btn-next {
    width: 200px;
    padding: 10px;
    font-weight: bold;
    color: var(--color-main);
    margin: 30px 5px;
    text-transform: uppercase;
}

.btn-prev {
    width: 48px;
    padding: 10px;
    color: var(--color-main);
    background-color: var(--color-3);
    margin: 30px 5px;
}

.btn-rounded-cube {
    width: 64px;
    height: 64px;
    background: var(--color-2);
    color: var(--color-main);
    border-radius: 15px;
    margin-right: 10px;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    font-size: 24px;
    font-weight: 300;
    cursor: default;
}

.btn-rounded-cube:not(:first-child)::before {
    content: "";
    width: 20px;
    height: 2px;
    background: var(--color-2);
    position: absolute;
    right: 64px;
}

.btn-rounded-cube.activated {
    background-color: var(--color-4);
}

.btn-rounded-cube.activated::before {
    background-color: var(--color-4);
}

input[type="text"], textarea, input[type="password"] {
    width: 300px;
    min-height: 48px;
    padding-left: 20px;
    box-sizing: border-box;
    border: 2px solid transparent;
    outline: 0;
    color: var(--color-text);
    background-color: var(--color-1);
    font-family: 'Titillium Web', sans-serif;
    caret-color: var(--color-4);
}

#tune-buttons input[type="text"] {
    width: 50%;
    height: 48px;
    min-height: 46px;
    padding-left: 20px;
    box-sizing: border-box;
    border: 2px solid transparent;
    outline: 0;
    color: var(--color-text);
    background-color: var(--color-1-transparent);
    font-family: 'Titillium Web', sans-serif;
    caret-color: var(--color-4);
}

#tune-buttons input[type="text"]:not(.no-bg):not(.no-filter) {
    backdrop-filter: blur(5px);
    background-attachment: fixed;
}
  
#tune-buttons button {
    box-sizing: border-box;
    background-color: var(--color-4);
    border: 0;
    color: var(--color-1);
    width: 25%;
    height: 48px;
    display: block;
    padding: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}
  
#tune-buttons button:hover {
      background-color: var(--color-main-bright);
}
  
#freq-down {
      border-radius: 15px 0 0 15px;
}
  
#freq-up {
      border-radius: 0 15px 15px 0;
}

.settings, .users-online-container, .chatbutton, .button-dark {
    background: transparent;
    border: 0;
    color: var(--color-4);
    font-size: 16px;
    width: 48px;
    height: 48px;
    text-align: center;
    border-radius: 15px;
    transition: 300ms ease background;
    cursor: pointer;
    margin: 2px;
}

.chatbutton, .settings, .button-dark {
    background-color: var(--color-1);
}

.settings:hover, .users-online-container:hover, .chatbutton:hover {
    background: var(--color-2);
}

input[type="range"] {
    margin: 0;
    /* removing default appearance */
    -webkit-appearance: none;
    appearance: none; 
    /* creating a custom design */
    width: 100%;
    cursor: pointer;
    outline: none;
    /*  slider progress trick  */
    overflow: hidden;
    border-radius: 15px;
    height: 100%;
    background: transparent;
}

/* Track: Mozilla Firefox */
input[type="range"]::-moz-range-track {
    height: 48px;
    background-color: transpraent;
    border-radius: 15px;
    border: 0;
}

input[type="range"]::-webkit-slider-thumb {
    /* removing default appearance */
    -webkit-appearance: none;
    appearance: none; 
    /* creating a custom design */
    height: 48px;
    width: 48px;
    background: url('../images/speaker.svg') center no-repeat, var(--color-5);
    background-position: center;
    background-size: 20px;
    border-radius: 15px;
    outline: 4px solid var(--color-5);
    outline-offset: -3px;
    /*  slider progress trick  */
    box-shadow: -420px 0 0 400px var(--color-4);
}

/* Thumb: Firefox */
input[type="range"]::-moz-range-thumb {
    box-sizing: border-box;
    height: 48px;
    width: 48px;
    background: url('../images/speaker.svg') center no-repeat, var(--color-5);
    background-position: center;
    background-size: 20px;
    border-radius: 15px;
    outline: 4px solid var(--color-5);
    outline-offset: -3px;
    /*  slider progress trick  */
    box-shadow: -420px 0 0 400px var(--color-4);
}

/*  Toggle Switch  */

.toggleSwitch span span {
    display: none;
}  

.toggleSwitch {
    user-select: none;
    display: inline-block;
    height: 48px;
    position: relative;
    overflow: hidden;
    padding: 0;
    cursor: pointer;
    width: 100%;
    border-radius: 15px;
    font-weight: bold;
    background-color: var(--color-2);
}
.toggleSwitch * {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.toggleSwitch input:focus ~ a,
.toggleSwitch input:focus + label {
    outline: none;
}
.toggleSwitch label {
    position: relative;
    z-index: 3;
    display: block;
    width: 100%;
}
.toggleSwitch input {
    position: absolute;
    opacity: 0;
    z-index: 5;
}
.toggleSwitch > span {
    position: absolute;
    left: 0;
    width: calc(100%);
    margin: 0;
    text-align: left;
    white-space: nowrap;
    margin:0;
}
.toggleSwitch > span span {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 5;
    display: block;
    width: 50%;
    margin-left: 50px;
    text-align: left;
    width: auto;
    opacity: 1;
    width: 50%;
    text-align: center;
    line-height:48px;
}
.toggleSwitch a {
    position: absolute;
    right: 50%;
    z-index: 4;
    display: block;
    top: 0;
    bottom: 0;
    padding: 0;
    left: 0;
    width: 50%;
    background-color: var(--color-4);
    border-radius: 15px;
    -webkit-transition: all 0.2s ease-out;
    -moz-transition: all 0.2s ease-out;
    transition: all 0.2s ease-out;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.toggleSwitch > span span:first-of-type {
    color: var(--color-1);
    opacity: 1;
    left: 0;
    margin: 0;
    width: 50%;
}
.toggleSwitch > span span:last-of-type {
    left:auto;
    right:0;
    color: var(--color-4);
    margin: 0;
    width: 50%;
}
.toggleSwitch > span:before {
    content: '';
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: -2px;
    border-radius: 15px;
    -webkit-transition: all 0.2s ease-out;
    -moz-transition: all 0.2s ease-out;
    transition: all 0.2s ease-out;
}
.toggleSwitch input:checked ~ a {
    left: calc(50%);
}

.toggleSwitch input:checked ~ span span:first-of-type {
    left:0;
    color: var(--color-4);
}
.toggleSwitch input:checked ~ span span:last-of-type {
    color: var(--color-1);
}

/*  End Toggle Switch  */

.switch {
    user-select: none;
}
.switch input[type=checkbox] {
    height: 0;
    width: 0;
    margin: 0;
}
.switch input[type=checkbox]:checked + label {
    background: var(--color-4);
}
.switch input[type=checkbox]:checked + label::after {
    left: calc(100% - 4px);
    transform: translateX(-100%);
    background-color: var(--color-1);
}
.switch label {
    cursor: pointer;
    min-width: 64px;
    max-width: 64px;
    height: 38px;
    background-color: var(--color-1);
    transition: 0.35s background-color;
    display: block;
    border-radius: 24px;
    margin: 0;
    position: relative;
    border: 2px solid var(--color-3);
}
.switch label::after {
    content: "";
    position: absolute;
    top: 4px;
    left: 4px;
    width: 26px;
    height: 26px;
    background: var(--color-5);
    border-radius: 16px;
    transition: 0.3s;
}

select {
    height: 42px;
    width: 150px;
    padding: 10px;
    background: var(--color-4);
    color: var(--color-1);
    border: 0;
    border-bottom: 4px solid var(--color-2);
    cursor: pointer;
    transition: 0.35s ease background;
    font-family: inherit;
    font-weight: bold;
}

select option {
    font-family: 'Titillium Web', sans-serif;
    font-weight: 300;
    padding: 10px;
    border: 0;
}

select:hover {
    background: var(--color-5);
}

.mini-popup {
    position: relative;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  .mini-popup .mini-popup-content {
    visibility: hidden;
    width: 230px;
    background-color: var(--color-2-transparent);
    backdrop-filter: blur(5px);
    color: var(--color-text);;
    text-align: center;
    border-radius: 15px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    top: 125%;
    left: 50%;
    margin-left: -115px;
    border: 3px solid var(--color-3);
  }
  
  .mini-popup .show {
    visibility: visible;
  }
  
  button[disabled] .mini-popup-content {
    visibility: hidden;
  }

  .mini-popup .mini-popup-content::after {
    content: "";
    position: absolute;
    bottom: calc(100% + 3px);
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent var(--color-3) transparent;
  }
  

@media only screen and (max-width: 768px) {
    #tune-buttons input[type="text"] {
        background-color: var(--color-1-transparent);
        height: 64px;
    }
    #tune-buttons button {
        height: 64px;
    }
}
