*[class^="panel-"] {
    background-color: var(--color-1-transparent);
    margin-left: 10px;
    margin-right: 10px;
    border-radius: 15px;
    text-align: center;
    margin-top: 20px;
    transition: 0.3s ease background-color;
}

*[class^="panel-"]:not(.no-bg):not(.no-filter):not(.w-100) {
    backdrop-filter: blur(5px);
}

.panel-100.w-100 {
    position: relative; 
}

.panel-100.w-100::before {
    content: '';
    backdrop-filter: blur(10px);
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    border-radius: 15px;
}

.panel-10 {
    width: 10%;
}

.panel-33 {
    width: 33%;
}

.panel-50 {
    width: 50%;
}

.panel-75 {
    width: 68%;
}

.panel-90 {
    width: 88%;
}

.panel-100 {
    width: 98%;
}

.panel-100-real {
    width: 100%;
}

.panel-full {
    margin-left: 0;
    margin-right: 0;
    width: 100%;
    max-width: 100% !important;
    transition: 0.35s ease;
    box-sizing: border-box;
}

@media only screen and (max-width: 768px) {
    .panel-75 {
        width: 90%;
    }
    .no-bg-phone {
        background-color: transparent;
    }
    .panel-33 h2 {
        padding: 20px;
        padding-top: 5px;
    }
    .panel-100, .panel-100.w-100 {
        width: 90% !important;
        margin: auto !important;
    } 
    [class^="panel-"] {
        margin: auto;
        width: 90%;
        margin-bottom: 20px;
    }
    *[class^="panel-"]:not(.no-bg):not(.no-filter):not(#ps-container),
    .panel-100.w-100::before {
        backdrop-filter: blur(0px);
    }
    .flex-phone {
        display: flex;
    }
    .panel-90 {
        margin-top: 100px;
    }
    .panel-100-real.bg-phone {
        background-color: var(--color-1-transparent);
        backdrop-filteR: blur(5px) !important;
        padding-left: 10px !important;
        padding-right: 10px !important;
    }
    #dashboard-panel-description {
        backdrop-filter: blur(25px) !important;
        width: 90%;
    }
}